// 字段类型缩写映射
const typeAbbreviations = {
    'string': '字符',
    'number': '数字',
    'boolean': '布尔',
    'array': '子表单',
    'list': '数组',
    'object': '对象',
    'date': '日期',
    'text': '字符',
    'textarea': '字符',
    'select': '字符',
    'checkbox': '布尔',
    'radio': '字符',
    'related': '关联表单'
};

// 类型转换工具函数
function getTypeAbbreviation(type) {
    return typeAbbreviations[type] || type;
}

// 节点类型定义
const nodeTypes = {
    'data-create': { name: '新增数据', category: 'data' },
    'data-update': { name: '更新数据', category: 'data' },
    'data-query': { name: '查询数据', category: 'data' },
    'data-delete': { name: '删除数据', category: 'data' },
    'page-message': { name: '创建消息', category: 'page' },
    'input-set-value': { name: '设置值', category: 'page' },
    'input-set-property': { name: '设置属性', category: 'page' },
    'input-visibility': { name: '显示隐藏', category: 'page' }
};

// 通用配置生成器
const nodeConfigs = {
    'data-create': {
        title: '新增数据配置',
        icon: '+',
        sections: [
            { type: 'form-selector', operation: 'create' },
            { type: 'input-output-params', operation: 'create', showDynamicFields: true }
        ]
    },
    'data-update': {
        title: '更新数据配置',
        icon: '✎',
        sections: [
            { type: 'form-selector', operation: 'update' },
            { type: 'filter-conditions', label: '更新条件', description: '设置更新的筛选条件' },
            { type: 'update-mode-selector' },
            { type: 'input-output-params', operation: 'update', showDynamicFields: true }
        ]
    },
    'data-query': {
        title: '查询数据配置',
        icon: '?',
        sections: [
            { type: 'form-selector', operation: 'query' },
            { type: 'filter-conditions', label: '查询条件', description: '设置查询的筛选条件' },
            { type: 'field-filter', label: '字段过滤', description: '选择要返回的字段（默认返回全部字段）' },
            { type: 'sort-pagination' },
            { type: 'output-params', operation: 'query' }
        ]
    },
    'data-delete': {
        title: '删除数据配置',
        icon: '×',
        sections: [
            { type: 'form-selector', operation: 'delete' },
            { type: 'filter-conditions', label: '删除条件', description: '不允许空条件' },
            { type: 'output-params', operation: 'delete' }
        ]
    },
    'page-message': {
        title: '页面消息配置',
        icon: '💬',
        sections: [
            { type: 'message-config' }
        ]
    },
    'input-set-value': {
        title: '页面设置值',
        icon: '📝',
        sections: [
            { type: 'component-selector', targetType: 'value' }
        ]
    },
    'input-set-property': {
        title: '页面设置属性',
        icon: '🔧',
        sections: [
            { type: 'component-selector', targetType: 'property' }
        ]
    },
    'input-visibility': {
        title: '页面显示隐藏',
        icon: '👁',
        sections: [
            { type: 'component-selector', targetType: 'visibility' }
        ]
    }
};

// 通用配置生成函数
function generateNodeConfig(nodeType) {
    const config = nodeConfigs[nodeType];
    if (!config) {
        return '<div class="empty-state"><h3>暂未实现</h3><p>该节点类型的配置功能正在开发中</p></div>';
    }

    let html = `<div class="form-section">`;

    config.sections.forEach(section => {
        html += generateConfigSection(section);
    });

    html += '</div>';
    return html;
}

// 配置区块生成函数
function generateConfigSection(section) {
    switch (section.type) {
        case 'form-selector':
            return generateFormSelector(section);
        case 'filter-conditions':
            return generateFilterConditions(section);
        case 'field-filter':
            return generateFieldFilter(section);
        case 'sort-pagination':
            return generateSortPagination();
        case 'update-mode-selector':
            return generateUpdateModeSelector();
        case 'input-output-params':
            return generateInputOutputParams(section.operation, section.showDynamicFields);
        case 'output-params':
            return generateOutputParamsOnly(section.operation);
        case 'message-config':
            return generateMessageConfig();
        case 'component-selector':
            return generateComponentSelector(section);
        default:
            return '';
    }
}

// 表单选择器
function generateFormSelector(section) {
    return `
        <div class="form-group">
            <label>目标表单</label>
            <select id="target-form" class="form-control" onchange="onTargetFormChange('${section.operation}')">
                <option value="">请选择表单</option>
                <option value="user-form">用户信息表单</option>
                <option value="resume-form">个人简历表单</option>
            </select>
        </div>
    `;
}

// 筛选条件
function generateFilterConditions(section) {
    return `
        <div class="form-group full-width">
            <label>${section.label} <small class="text-muted">(${section.description})</small></label>
            <div id="filter-conditions" class="condition-builder">
                <div id="filter-rows-container"></div>
                <button type="button" onclick="addFilterCondition()" class="btn btn-small" style="margin-top: 10px;">+ 添加条件</button>
            </div>
        </div>
    `;
}

// 排序和分页
function generateSortPagination() {
    return `
        <div class="form-row">
            <div class="form-group">
                <label>排序字段</label>
                <input type="text" id="sort-field" class="form-control" placeholder="例如: created_at">
            </div>
            <div class="form-group">
                <label>排序方式</label>
                <select id="sort-order" class="form-control">
                    <option value="asc">升序</option>
                    <option value="desc">降序</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>每页记录数</label>
                <input type="number" id="page-size" class="form-control" value="20" min="1" max="100" placeholder="20">
            </div>
            <div class="form-group">
                <label>页码</label>
                <input type="number" id="page-number" class="form-control" value="1" min="1" placeholder="1">
            </div>
        </div>
    `;
}

// 字段过滤器
function generateFieldFilter(section) {
    return `
        <div class="form-group full-width">
            <label>${section.label} <small class="text-muted">(${section.description})</small></label>
            <div class="field-filter-container">
                <div id="field-filter-dropdown-container" class="field-filter-select-container">
                    <!-- 多选下拉组件将在这里渲染 -->
                </div>
            </div>
        </div>
    `;
}

// 消息配置
function generateMessageConfig() {
    return `
        <div class="form-group">
            <label>消息类型</label>
            <select id="message-type" class="form-control">
                <option value="info">普通信息</option>
                <option value="success">成功消息</option>
                <option value="warning">警告消息</option>
                <option value="error">错误消息</option>
            </select>
        </div>
        <div class="form-group">
            <label>显示位置</label>
            <select id="message-position" class="form-control">
                <option value="top">顶部</option>
                <option value="center">中央</option>
                <option value="bottom">底部</option>
            </select>
        </div>
        <div class="form-group">
            <label>消息内容</label>
            <div class="expression-validator">
                <textarea id="message-content" class="form-control textarea" placeholder="输入消息内容，支持{{}}表达式&#10;例如: 操作成功！共处理了 {{result.count}} 条数据。"
                    oninput="validateExpressionInput(this); handleExpressionInput(this)"
                    onblur="hideExpressionValidation(this)"
                    onkeydown="handleExpressionKeydown(event, this)"></textarea>
                <div class="expression-validation" id="validation-message-content"></div>
                <div class="autocomplete-popup" id="autocomplete-message-content"></div>
            </div>
        </div>
        <div class="form-group">
            <label>显示时长(秒)</label>
            <input type="number" id="message-duration" class="form-control" value="3" min="1" max="60">
        </div>
        <div class="form-group">
            <label>是否可关闭</label>
            <select id="message-closable" class="form-control">
                <option value="true">可关闭</option>
                <option value="false">不可关闭</option>
            </select>
        </div>
    `;
}

// 组件选择器
function generateComponentSelector(section) {
    const targetType = section.targetType;
    
    if (targetType === 'value') {
        return generateValueConfig();
    } else if (targetType === 'property') {
        return generatePropertyConfig();
    } else if (targetType === 'visibility') {
        return generateVisibilityConfig();
    }
    return '';
}

// 设置值配置
function generateValueConfig() {
    return `
        <div class="form-group" style="display: none;">
            <label>目标组件</label>
            <select id="target-component" class="form-control">
                <option value="">选择目标组件</option>
                <option value="current-form" selected>当前表单字段</option>
                <option value="other-form">其他表单字段</option>
            </select>
        </div>
        <div class="form-group" style="display: none;">
            <label>表单选择</label>
            <select id="target-form-select" class="form-control" onchange="onTargetFormSelectChange()">
                <option value="user-form">用户信息表单</option>
                <option value="resume-form" selected>个人简历表单</option>
            </select>
        </div>
        <div class="form-group">
            <label>字段名称</label>
            <select id="target-field" class="form-control" onchange="onTargetFieldChange()">
                <option value="">选择字段</option>
            </select>
        </div>
        <div id="expression-config" class="form-group">
            <label>表达式</label>
            <div class="expression-validator">
                <input type="text" id="input-expression" class="form-control" placeholder="例如: {{form.firstName}} + ' ' + {{form.lastName}}"
                    oninput="validateExpressionInput(this); handleExpressionInput(this)"
                    onblur="hideExpressionValidation(this)"
                    onkeydown="handleExpressionKeydown(event, this)">
                <div class="expression-validation" id="validation-input-expression"></div>
                <div class="autocomplete-popup" id="autocomplete-input-expression"></div>
            </div>
        </div>
        <div id="subform-config" style="display: none;">
            <div class="form-group full-width">
                <div class="subform-mode-selector">
                    <div class="mode-option">
                        <input type="radio" id="subform-mode-append" name="subform-mode" value="append" checked onchange="onSubformModeChange('append')">
                        <label for="subform-mode-append" class="mode-label">
                            <span class="mode-title">新增模式 - 基于原数据新增</span>
                        </label>
                    </div>
                    <div class="mode-option">
                        <input type="radio" id="subform-mode-overwrite" name="subform-mode" value="overwrite" onchange="onSubformModeChange('overwrite')">
                        <label for="subform-mode-overwrite" class="mode-label">
                            <span class="mode-title">覆盖模式 - 原数据清空再新增</span>
                        </label>
                    </div>
                </div>
                <label>子表单数据编辑</label>
                <div id="subform-container"></div>
            </div>
        </div>
    `;
}

// 设置属性配置
function generatePropertyConfig() {
    return `
        <div class="form-group">
            <label>目标组件</label>
            <select id="target-component-prop" class="form-control">
                <option value="">选择组件字段</option>
            </select>
        </div>
        <div class="form-group">
            <label>属性名称</label>
            <select id="property-name" class="form-control">
                <option value="disabled">禁用状态</option>
                <option value="readonly">只读状态</option>
                <option value="required">必填状态</option>
                <option value="placeholder">占位符</option>
                <option value="maxLength">最大长度</option>
            </select>
        </div>
        <div class="form-group">
            <label>表达式</label>
            <div class="expression-validator">
                <input type="text" id="property-value" class="form-control" placeholder="例如: {{form.isAdvanced}} 或 'disabled'"
                    oninput="validateExpressionInput(this); handleExpressionInput(this)"
                    onblur="hideExpressionValidation(this)"
                    onkeydown="handleExpressionKeydown(event, this)">
                <div class="expression-validation" id="validation-property-value"></div>
                <div class="autocomplete-popup" id="autocomplete-property-value"></div>
            </div>
        </div>
    `;
}

// 显示隐藏配置
function generateVisibilityConfig() {
    return `
        <div class="form-group">
            <label>目标组件</label>
            <select id="target-component-visibility" class="form-control">
                <option value="">选择组件字段</option>
            </select>
        </div>
        <div class="form-group">
            <label>操作类型</label>
            <select id="visibility-action" class="form-control">
                <option value="show">显示</option>
                <option value="hide">隐藏</option>
                <option value="toggle">切换</option>
            </select>
        </div>
        <div class="form-group full-width" id="condition-config" style="display: none;">
            <label>条件表达式</label>
            <div class="expression-validator">
                <input type="text" id="visibility-condition" class="form-control" placeholder="例如: {{form.type}} === 'advanced'"
                    oninput="validateExpressionInput(this); handleExpressionInput(this)"
                    onblur="hideExpressionValidation(this)"
                    onkeydown="handleExpressionKeydown(event, this)">
                <div class="expression-validation" id="validation-visibility-condition"></div>
                <div class="autocomplete-popup" id="autocomplete-visibility-condition"></div>
            </div>
        </div>
        <div class="form-group">
            <label>动画效果</label>
            <select id="visibility-animation" class="form-control">
                <option value="none">无动画</option>
                <option value="fade">淡入淡出</option>
            </select>
        </div>
        <div class="form-group">
            <label>动画时长(ms)</label>
            <input type="number" id="animation-duration" class="form-control" value="300" min="0" max="2000">
        </div>
    `;
}

// 生成Input和Output参数配置
function generateInputOutputParams(nodeType, showDynamicFields = false) {
    const inputParams = []; // 直接使用空子表单替代 getInputParams(nodeType)
    const outputParams = getOutputParams(nodeType);
    
    // 字段配置区块（如果需要显示）
    const dynamicFieldsSection = showDynamicFields ? `
        <div id="dynamic-fields-container" style="display: none;">
            <div id="fields-list"></div>
        </div>
    ` : '';
    
    return `
        <div class="param-section">
            <div class="param-header input">
                <span>📥</span>Input Parameters (输入参数)
            </div>
            <div class="param-content">
                ${dynamicFieldsSection}
                ${inputParams.map(param => generateParamItem(param, 'input')).join('')}
            </div>
        </div>
        
        <div class="param-section">
            <div class="param-header output">
                <span>📤</span>Output Parameters (输出参数)
            </div>
            <div class="param-content">
                ${outputParams.length > 0 ? `
                <div class="param-row param-header-row">
                    <div class="param-cell param-name-cell">字段名</div>
                    <div class="param-cell param-type-cell">字段类型</div>
                    <div class="param-cell param-value-cell">字段值</div>
                </div>
                ` : ''}
                ${outputParams.map(param => generateParamItem(param, 'output')).join('')}
            </div>
        </div>
    `;
}

// 生成仅Output参数配置
function generateOutputParamsOnly(nodeType) {
    const outputParams = getOutputParams(nodeType);
    
    return `
        <div class="param-section">
            <div class="param-header output">
                <span>📤</span>Output Parameters (输出参数)
            </div>
            <div class="param-content">
                ${outputParams.length > 0 ? `
                <div class="param-row param-header-row">
                    <div class="param-cell param-name-cell">字段名</div>
                    <div class="param-cell param-type-cell">字段类型</div>
                    <div class="param-cell param-value-cell">字段值</div>
                </div>
                ` : ''}
                ${outputParams.map(param => generateParamItem(param, 'output')).join('')}
            </div>
        </div>
    `;
}

// 生成参数项
function generateParamItem(param, paramType) {
    const isOutput = paramType === 'output';
    const typeAbbr = getTypeAbbreviation(param.type);
    const requiredMark = param.required ? '*' : '';
    let placeholder = isOutput ? (param.defaultValue || '') : '表达式';
    
    // 如果是关联表单类型，生成包含示例 ID 的 placeholder
    if (!isOutput && param.type === 'related' && param.relatedFormId) {
        placeholder = `例如: ${param.relatedFormId}_001`;
    }
    
    if (isOutput) {
        // 输出参数使用可编辑的变量名称输入框
        return `
            <div class="param-row param-item-row">
                <div class="param-cell param-name-cell">
                    <span class="param-name">${param.name}${requiredMark}</span>
                </div>
                <div class="param-cell param-type-cell">
                    <span class="param-type ${paramType}">${typeAbbr}</span>
                </div>
                <div class="param-cell param-value-cell">
                    <input type="text"
                           class="param-input variable-input"
                           placeholder="变量"
                           value=""
                           title="${param.description}"
                           onblur="validateVariableName(this)"
                           data-original-name="${param.name}">
                </div>
            </div>
        `;
    } else {
        // 输入参数保持原有样式
        return `
            <div class="param-row param-item-row">
                <div class="param-cell param-name-cell">
                    <span class="param-name">${param.name}${requiredMark}</span>
                </div>
                <div class="param-cell param-type-cell">
                    <span class="param-type ${paramType}">${typeAbbr}</span>
                </div>
                <div class="param-cell param-value-cell">
                    <input type="text" class="param-input readonly-param" value="${placeholder}" readonly title="${param.description}">
                </div>
            </div>
        `;
    }
}



// 获取输出参数定义
function getOutputParams(nodeType) {
    const outputParams = {
        'create': [
            { name: '记录ID', type: 'string', description: '新创建记录的ID', defaultValue: '' },
            { name: '成功状态', type: 'boolean', description: '操作是否成功', defaultValue: '' },
            { name: '错误信息', type: 'string', description: '错误信息（失败时）', defaultValue: '' },
            { name: '影响行数', type: 'number', description: '影响的记录数', defaultValue: '' }
        ],
        'update': [
            { name: '影响行数', type: 'number', description: '更新的记录数', defaultValue: '' },
            { name: '成功状态', type: 'boolean', description: '操作是否成功', defaultValue: '' },
            { name: '错误信息', type: 'string', description: '错误信息（失败时）', defaultValue: '' }
        ],
        'query': [
            { name: '数据列表', type: 'list', description: '查询结果数据', defaultValue: '' },
            { name: '总记录数', type: 'number', description: '总记录数', defaultValue: '' },
            { name: '总页数', type: 'number', description: '总页数', defaultValue: '' },
            { name: '当前页码', type: 'number', description: '当前页码', defaultValue: '' },
            { name: '成功状态', type: 'boolean', description: '操作是否成功', defaultValue: '' }
        ],
        'delete': [
            { name: '影响行数', type: 'number', description: '删除的记录数', defaultValue: '' },
            { name: '成功状态', type: 'boolean', description: '操作是否成功', defaultValue: '' },
            { name: '错误信息', type: 'string', description: '错误信息（失败时）', defaultValue: '' }
        ]
    };
    
    return outputParams[nodeType] || [];
}

// 更新模式选择器
function generateUpdateModeSelector() {
    return `
        <div class="form-group full-width">
            <label>更新模式</label>
            <div class="update-mode-selector">
                <div class="mode-option">
                    <input type="radio" id="mode-null" name="update-mode" value="null" checked onchange="onUpdateModeChange('null')">
                    <label for="mode-null" class="mode-label">
                        <span class="mode-title">置空模式</span>
                        <span class="mode-description">未填写的字段设置为空（子表单忽略）</span>
                    </label>
                </div>
                <div class="mode-option">
                    <input type="radio" id="mode-retain" name="update-mode" value="retain" onchange="onUpdateModeChange('retain')">
                    <label for="mode-retain" class="mode-label">
                        <span class="mode-title">保留模式</span>
                        <span class="mode-description">未填写的字段不修改（保持原值）</span>
                    </label>
                </div>
            </div>
        </div>
    `;
}