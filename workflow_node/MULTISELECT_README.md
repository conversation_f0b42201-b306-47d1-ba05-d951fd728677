# 多选下拉组件 (MultiSelectDropdown)

## 概述

这是一个现代化的多选下拉组件，用于替换原有的原生HTML多选框，提供更好的用户体验和功能。

## 功能特性

- ✅ **现代化UI设计** - 美观的下拉框样式，符合现代Web设计标准
- ✅ **搜索功能** - 支持实时搜索过滤选项
- ✅ **标签显示** - 已选择项目以标签形式显示，可单独删除
- ✅ **全选/清空** - 提供快捷的全选和清空操作
- ✅ **响应式设计** - 适配移动端和桌面端
- ✅ **键盘支持** - 支持ESC键关闭下拉框
- ✅ **外部点击关闭** - 点击组件外部自动关闭下拉框
- ✅ **API兼容** - 与原有代码保持API兼容性

## 使用方法

### 基础用法

```javascript
// 创建多选下拉组件
const dropdown = new MultiSelectDropdown(container, {
    options: [
        { value: 'option1', text: '选项1' },
        { value: 'option2', text: '选项2' },
        { value: 'option3', text: '选项3' }
    ],
    placeholder: '请选择选项',
    onChange: (selectedValues) => {
        console.log('选中的值:', selectedValues);
    }
});
```

### 配置选项

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `options` | Array | `[]` | 选项数组，每个选项包含 `value` 和 `text` |
| `selectedValues` | Array | `[]` | 初始选中的值 |
| `placeholder` | String | `'请选择字段'` | 占位符文本 |
| `searchPlaceholder` | String | `'搜索字段...'` | 搜索框占位符 |
| `onChange` | Function | `() => {}` | 选择变化回调函数 |

### API方法

```javascript
// 获取选中的值
const values = dropdown.getValue();

// 设置选中的值
dropdown.setValue(['option1', 'option2']);

// 更新选项
dropdown.setOptions(newOptions);

// 全选
dropdown.selectAll();

// 清空选择
dropdown.clearAll();

// 销毁组件
dropdown.destroy();
```

## 在字段过滤器中的应用

组件已集成到工作流节点配置的字段过滤器中：

1. **选择查询数据节点** - 点击"查询数据"节点卡片
2. **选择目标表单** - 在配置面板中选择要查询的表单
3. **使用字段过滤器** - 在"字段过滤"部分使用新的多选下拉组件

### 字段过滤器特性

- 显示字段名称、标签和类型信息
- 支持子表单字段（如 `experience.company`）
- 搜索功能可按字段名或标签搜索
- 已选择字段以标签形式显示在下方
- 未选择任何字段时默认返回全部字段

## 样式定制

组件使用项目现有的CSS变量，确保样式一致性：

```css
/* 主要CSS变量 */
--primary-color: #3498db;
--bg-white: #ffffff;
--bg-light: #f8f9fa;
--border-default: #dee2e6;
--text-dark: #2c3e50;
--spacing-sm: 8px;
--radius-sm: 4px;
```

## 响应式设计

组件针对不同屏幕尺寸进行了优化：

- **桌面端** (>768px) - 完整功能和布局
- **平板端** (≤768px) - 调整按钮布局和标签大小
- **移动端** (≤480px) - 优化触摸交互和显示效果

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 测试

### 测试页面

访问 `test-multiselect.html` 查看组件的各种使用场景：

1. **基础多选测试** - 基本的多选功能
2. **字段过滤器测试** - 模拟实际使用场景
3. **预设选中值测试** - 测试初始化选中状态

### 演示脚本

可以启用 `demo-script.js` 来自动演示组件功能：

```html
<!-- 取消注释以启用演示 -->
<script src="demo-script.js"></script>
```

## 文件结构

```
workflow_node/
├── js/
│   ├── multi-select-dropdown.js    # 多选下拉组件核心代码
│   ├── node-config.js              # 更新了字段过滤器生成函数
│   └── ui-interaction.js           # 更新了字段过滤器相关函数
├── styles.css                      # 添加了组件样式
├── test-multiselect.html           # 测试页面
├── demo-script.js                  # 演示脚本
└── MULTISELECT_README.md           # 本文档
```

## 迁移说明

从原生多选框迁移到新组件：

### 之前的代码
```html
<select multiple onchange="onFieldFilterChange()">
    <option value="field1">字段1</option>
    <option value="field2">字段2</option>
</select>
```

### 现在的代码
```javascript
const dropdown = new MultiSelectDropdown(container, {
    options: [
        { value: 'field1', text: '字段1' },
        { value: 'field2', text: '字段2' }
    ],
    onChange: onFieldFilterChange
});
```

## 注意事项

1. **事件兼容性** - 组件会触发原有的 `onFieldFilterChange` 事件以保持兼容性
2. **数据格式** - 选项数据需要包含 `value` 和 `text` 属性
3. **容器要求** - 需要提供一个DOM容器元素来渲染组件
4. **内存管理** - 不再需要组件时应调用 `destroy()` 方法清理资源

## 故障排除

### 常见问题

1. **组件未显示** - 检查容器元素是否存在，CSS文件是否正确加载
2. **样式异常** - 确认CSS变量已定义，检查样式冲突
3. **功能异常** - 检查JavaScript文件加载顺序，确认无控制台错误

### 调试方法

```javascript
// 在控制台中检查组件状态
console.log('组件实例:', fieldFilterDropdown);
console.log('选中值:', fieldFilterDropdown?.getValue());
console.log('选项数据:', fieldFilterDropdown?.options);
```
